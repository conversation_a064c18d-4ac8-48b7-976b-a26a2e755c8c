#!/usr/bin/env python3
"""
🧪 TEST PRODUCTION GAIT CLASSIFIER
Quick validation script for the production model
"""

import os
import pandas as pd
import numpy as np
import pickle
from sklearn.metrics import accuracy_score
import warnings
warnings.filterwarnings('ignore')

print("🧪 TESTING PRODUCTION GAIT CLASSIFIER")
print("="*50)

def load_production_model():
    """Load the production model."""
    model_file = "production_gait_classifier.pkl"
    
    if not os.path.exists(model_file):
        print(f"❌ Model not found: {model_file}")
        print("Please run production_gait_classifier.py first!")
        return None
    
    try:
        with open(model_file, 'rb') as f:
            model_data = pickle.load(f)
        
        print(f"✅ Production model loaded: {model_file}")
        print(f"📊 Trained on {model_data['training_samples']:,} samples")
        print(f"🔍 Uses {len(model_data['selected_features'])} features")
        print(f"🏥 Supports {len(model_data['diseases'])} diseases")
        
        return model_data
    
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return None

def quick_test_model(model_data):
    """Quick test of the production model."""
    print("\n🚀 QUICK MODEL TEST")
    print("-" * 30)
    
    # Load model components
    rf_model = model_data['rf_model']
    svm_model = model_data['svm_model']
    scaler = model_data['scaler']
    le = model_data['label_encoder']
    
    # Test with sample data from AB2948
    test_subject = "AB2948"
    test_path = f"datasets/{test_subject}/{test_subject}/Features"
    
    if not os.path.exists(test_path):
        print(f"❌ Test data not found: {test_path}")
        return
    
    # Load one test file
    test_files = [f for f in os.listdir(test_path) if f.endswith('.csv')]
    if not test_files:
        print("❌ No test files found")
        return
    
    test_file = test_files[0]
    print(f"📂 Testing with: {test_file}")
    
    try:
        # Load test data
        df = pd.read_csv(f"{test_path}/{test_file}")
        print(f"📊 Test data shape: {df.shape}")
        
        # Create feature matrix
        training_features = model_data['selected_features']
        X_test = pd.DataFrame(index=df.index)
        
        available_features = 0
        for feature in training_features:
            if feature in df.columns:
                X_test[feature] = df[feature]
                available_features += 1
            else:
                X_test[feature] = 0
        
        print(f"🔍 Features available: {available_features}/{len(training_features)}")
        
        # Clean and scale
        X_test_clean = X_test.fillna(0)
        X_test_scaled = scaler.transform(X_test_clean)
        
        # Handle NaN values
        if np.isnan(X_test_scaled).any():
            X_test_scaled = np.nan_to_num(X_test_scaled, nan=0.0)
        
        print(f"📊 Processed test data: {X_test_scaled.shape}")
        
        # Test predictions
        rf_pred = rf_model.predict(X_test_scaled)
        svm_pred = svm_model.predict(X_test_scaled)
        
        # Convert to class names
        rf_classes = le.inverse_transform(rf_pred)
        svm_classes = le.inverse_transform(svm_pred)
        
        # Show predictions
        print(f"\n🌲 Random Forest Predictions:")
        rf_unique, rf_counts = np.unique(rf_classes, return_counts=True)
        for cls, count in zip(rf_unique, rf_counts):
            print(f"   {cls}: {count} samples ({count/len(rf_classes)*100:.1f}%)")
        
        print(f"\n🤖 SVM Predictions:")
        svm_unique, svm_counts = np.unique(svm_classes, return_counts=True)
        for cls, count in zip(svm_unique, svm_counts):
            print(f"   {cls}: {count} samples ({count/len(svm_classes)*100:.1f}%)")
        
        # Agreement
        agreement = np.sum(rf_pred == svm_pred) / len(rf_pred)
        print(f"\n🤝 Model Agreement: {agreement:.4f} ({agreement*100:.2f}%)")
        
        # Most common predictions
        rf_most_common = rf_unique[np.argmax(rf_counts)]
        svm_most_common = svm_unique[np.argmax(svm_counts)]
        
        print(f"\n🎯 Dominant Predictions:")
        print(f"   Random Forest: {rf_most_common}")
        print(f"   SVM: {svm_most_common}")
        
        if rf_most_common == svm_most_common:
            print(f"✅ Both models agree: {rf_most_common}")
        else:
            print(f"⚠️ Models disagree")
        
        print(f"\n✅ Quick test completed successfully!")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

def show_model_info(model_data):
    """Show detailed model information."""
    print("\n📋 PRODUCTION MODEL INFORMATION")
    print("-" * 40)
    
    print(f"🔍 Features: {len(model_data['selected_features'])}")
    print(f"📊 Training samples: {model_data['training_samples']:,}")
    print(f"🏥 Diseases supported: {len(model_data['diseases'])}")
    
    print(f"\n🎯 Supported Diseases:")
    for i, (disease, desc) in enumerate(model_data['diseases'].items(), 1):
        print(f"   {i:2d}. {disease}")
    
    print(f"\n🤖 Model Classes (actually trained):")
    le = model_data['label_encoder']
    for i, cls in enumerate(le.classes_, 1):
        print(f"   {i}. {cls}")

def main():
    print("🚀 Starting production model test...")
    
    # Load production model
    model_data = load_production_model()
    if model_data is None:
        return
    
    # Show model information
    show_model_info(model_data)
    
    # Quick test
    quick_test_model(model_data)
    
    print(f"\n🎯 PRODUCTION MODEL TEST SUMMARY:")
    print(f"   Model file: production_gait_classifier.pkl")
    print(f"   Status: ✅ Working")
    print(f"   Features: {len(model_data['selected_features'])}")
    print(f"   Training samples: {model_data['training_samples']:,}")
    print(f"   Diseases defined: {len(model_data['diseases'])}")
    print(f"   Classes trained: {len(model_data['label_encoder'].classes_)}")
    
    print(f"\n✅ PRODUCTION MODEL VALIDATION COMPLETE!")

if __name__ == "__main__":
    main()
