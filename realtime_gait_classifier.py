#!/usr/bin/env python3
"""
🚀 REAL-TIME GAIT DISEASE CLASSIFIER
Receives data from Arduino and classifies gait patterns in real-time
"""

import serial
import numpy as np
import pandas as pd
import pickle
import time
import json
from collections import deque
import warnings
warnings.filterwarnings('ignore')

print("🚀 REAL-TIME GAIT DISEASE CLASSIFIER")
print("🔬 Connecting to Arduino sensors...")
print("="*50)

class RealTimeGaitClassifier:
    def __init__(self, model_path="production_gait_classifier.pkl", port="COM3", baudrate=115200):
        """Initialize real-time classifier."""
        
        # Load trained model
        print(f"📊 Loading model: {model_path}")
        try:
            with open(model_path, 'rb') as f:
                self.model_data = pickle.load(f)
            
            self.rf_model = self.model_data['rf_model']
            self.svm_model = self.model_data['svm_model']
            self.scaler = self.model_data['scaler']
            self.label_encoder = self.model_data['label_encoder']
            self.selected_features = self.model_data['selected_features']
            
            print(f"✅ Model loaded successfully")
            print(f"🔍 Features: {len(self.selected_features)}")
            print(f"🏥 Classes: {len(self.label_encoder.classes_)}")
            
        except Exception as e:
            print(f"❌ Error loading model: {e}")
            return
        
        # Initialize serial connection
        print(f"🔌 Connecting to Arduino on {port}...")
        try:
            self.serial_conn = serial.Serial(port, baudrate, timeout=1)
            time.sleep(2)  # Wait for Arduino to initialize
            print(f"✅ Connected to Arduino")
        except Exception as e:
            print(f"❌ Error connecting to Arduino: {e}")
            return
        
        # Data buffers
        self.data_buffer = deque(maxlen=10)  # Keep last 10 windows
        self.classification_history = deque(maxlen=20)  # Keep last 20 classifications
        
        print(f"🎯 Real-time classifier ready!")
    
    def extract_emg_features(self, emg_data):
        """Extract EMG features from raw signal."""
        features = {}
        
        muscle_names = ['R_Soleus', 'L_Soleus', 'R_rectus femoris', 'L_Rectus Femoris', 
                       'R_Medial Gastrocnemius', 'L_Medial Gastrocnemius']
        
        for i, muscle in enumerate(muscle_names):
            if i < len(emg_data):
                signal = np.array(emg_data[i])
                
                # MAV (Mean Absolute Value)
                mav = np.mean(np.abs(signal))
                features[f"{muscle}_EMG 1 MAV"] = mav
                
                # WL (Waveform Length)
                wl = np.sum(np.abs(np.diff(signal)))
                features[f"{muscle}_EMG 1 WL"] = wl
                
                # ZC (Zero Crossings)
                zc = np.sum(np.diff(np.sign(signal)) != 0)
                features[f"{muscle}_EMG 1 ZC"] = zc
                
                # SS (Slope Sign)
                diff_signal = np.diff(signal)
                ss = np.sum(np.diff(np.sign(diff_signal)) != 0)
                features[f"{muscle}_EMG 1 SS"] = ss
        
        return features
    
    def extract_imu_features(self, acc_data, gyro_data):
        """Extract IMU features from accelerometer and gyroscope data."""
        features = {}
        
        muscle_names = ['R_Soleus', 'L_Soleus', 'R_rectus femoris', 'L_Rectus Femoris']
        axes = ['X', 'Y', 'Z']
        stats = ['mean', 'std_dev', 'max']
        
        # Process ACC data
        for muscle_idx, muscle in enumerate(muscle_names):
            for axis_idx, axis in enumerate(axes):
                if axis_idx < len(acc_data):
                    signal = np.array(acc_data[axis_idx])
                    
                    features[f"{muscle}_ACC {axis} mean"] = np.mean(signal)
                    features[f"{muscle}_ACC {axis} std_dev"] = np.std(signal)
                    features[f"{muscle}_ACC {axis} max"] = np.max(signal)
        
        # Process GYRO data
        for muscle_idx, muscle in enumerate(muscle_names):
            for axis_idx, axis in enumerate(axes):
                if axis_idx < len(gyro_data):
                    signal = np.array(gyro_data[axis_idx])
                    
                    features[f"{muscle}_GYRO {axis} mean"] = np.mean(signal)
                    features[f"{muscle}_GYRO {axis} std_dev"] = np.std(signal)
                    features[f"{muscle}_GYRO {axis} max"] = np.max(signal)
        
        return features
    
    def create_feature_vector(self, emg_features, imu_features):
        """Create feature vector matching the trained model."""
        
        # Combine all features
        all_features = {**emg_features, **imu_features}
        
        # Create feature vector with same order as training
        feature_vector = []
        for feature_name in self.selected_features:
            if feature_name in all_features:
                feature_vector.append(all_features[feature_name])
            else:
                feature_vector.append(0)  # Fill missing features with 0
        
        return np.array(feature_vector).reshape(1, -1)
    
    def classify_gait(self, feature_vector):
        """Classify gait pattern using trained models."""
        
        # Scale features
        scaled_features = self.scaler.transform(feature_vector)
        
        # Handle NaN values
        if np.isnan(scaled_features).any():
            scaled_features = np.nan_to_num(scaled_features, nan=0.0)
        
        # Get predictions from both models
        rf_pred = self.rf_model.predict(scaled_features)[0]
        svm_pred = self.svm_model.predict(scaled_features)[0]
        
        # Get class names
        rf_class = self.label_encoder.inverse_transform([rf_pred])[0]
        svm_class = self.label_encoder.inverse_transform([svm_pred])[0]
        
        # Ensemble decision (prefer agreement, fallback to RF)
        if rf_class == svm_class:
            final_prediction = rf_class
            confidence = "High"
        else:
            final_prediction = rf_class  # Use RF as tiebreaker
            confidence = "Medium"
        
        return {
            'prediction': final_prediction,
            'rf_prediction': rf_class,
            'svm_prediction': svm_class,
            'confidence': confidence
        }
    
    def parse_arduino_data(self, data_lines):
        """Parse data received from Arduino."""
        emg_data = [[] for _ in range(6)]
        acc_data = [[] for _ in range(3)]
        gyro_data = [[] for _ in range(3)]
        
        for line in data_lines:
            line = line.strip()
            if ':' in line:
                key, values = line.split(':', 1)
                value_list = [float(x) for x in values.split(',')]
                
                if key.startswith('EMG_'):
                    idx = int(key.split('_')[1])
                    if idx < 6:
                        emg_data[idx] = value_list
                elif key.startswith('ACC_'):
                    idx = int(key.split('_')[1])
                    if idx < 3:
                        acc_data[idx] = value_list
                elif key.startswith('GYRO_'):
                    idx = int(key.split('_')[1])
                    if idx < 3:
                        gyro_data[idx] = value_list
        
        return emg_data, acc_data, gyro_data
    
    def run_realtime_classification(self):
        """Main loop for real-time classification."""
        print(f"\n🔬 Starting real-time gait classification...")
        print(f"📊 Waiting for Arduino data...")
        
        data_buffer = []
        in_data_block = False
        
        try:
            while True:
                if self.serial_conn.in_waiting > 0:
                    line = self.serial_conn.readline().decode('utf-8').strip()
                    
                    if line == "DATA_START":
                        in_data_block = True
                        data_buffer = []
                    elif line == "DATA_END" and in_data_block:
                        in_data_block = False
                        
                        # Process the data
                        try:
                            emg_data, acc_data, gyro_data = self.parse_arduino_data(data_buffer)
                            
                            # Extract features
                            emg_features = self.extract_emg_features(emg_data)
                            imu_features = self.extract_imu_features(acc_data, gyro_data)
                            
                            # Create feature vector
                            feature_vector = self.create_feature_vector(emg_features, imu_features)
                            
                            # Classify
                            result = self.classify_gait(feature_vector)
                            
                            # Store in history
                            self.classification_history.append(result['prediction'])
                            
                            # Display result
                            print(f"\n🎯 CLASSIFICATION RESULT:")
                            print(f"   Prediction: {result['prediction']}")
                            print(f"   RF: {result['rf_prediction']}")
                            print(f"   SVM: {result['svm_prediction']}")
                            print(f"   Confidence: {result['confidence']}")
                            
                            # Show recent history
                            if len(self.classification_history) >= 5:
                                recent = list(self.classification_history)[-5:]
                                print(f"   Recent: {' → '.join(recent)}")
                            
                        except Exception as e:
                            print(f"⚠️ Processing error: {e}")
                    
                    elif in_data_block:
                        data_buffer.append(line)
                    elif line == "GAIT_SENSOR_READY":
                        print(f"✅ Arduino sensor ready!")
                
                time.sleep(0.001)  # Small delay to prevent CPU overload
                
        except KeyboardInterrupt:
            print(f"\n🛑 Stopping real-time classification...")
        except Exception as e:
            print(f"❌ Error: {e}")
        finally:
            self.serial_conn.close()
            print(f"🔌 Serial connection closed")

def main():
    # Initialize classifier (adjust COM port as needed)
    classifier = RealTimeGaitClassifier(
        model_path="production_gait_classifier.pkl",
        port="COM3",  # Change this to your Arduino port
        baudrate=115200
    )
    
    # Start real-time classification
    classifier.run_realtime_classification()

if __name__ == "__main__":
    main()
