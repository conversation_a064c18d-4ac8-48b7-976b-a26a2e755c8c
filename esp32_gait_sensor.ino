/*
🔬 ESP32 REAL-TIME GAIT SENSOR SYSTEM
8 EMG patches + 4 IMU sensors + WiFi/Bluetooth transmission
For Arduino IDE with ESP32 board package
*/

#include <WiFi.h>
#include <WebSocketsServer.h>
#include <Wire.h>
#include <MPU6050.h>
#include <ArduinoJson.h>

// WiFi credentials
const char* ssid = "YOUR_WIFI_SSID";
const char* password = "YOUR_WIFI_PASSWORD";

// WebSocket server for real-time data transmission
WebSocketsServer webSocket = WebSocketsServer(81);

// EMG sensor pins (8 AD8232 sensors)
const int EMG_PINS[8] = {
  36, 39, 34, 35,  // ADC1 pins for left thigh, right thigh
  32, 33, 25, 26   // ADC1 pins for left calf, right calf
};

// MPU6050 IMU sensors (4 sensors on I2C multiplexer or different I2C buses)
MPU6050 imu_sensors[4];
const int I2C_SDA_PINS[4] = {21, 22, 16, 17};  // SDA pins for 4 IMUs
const int I2C_SCL_PINS[4] = {19, 23, 4, 2};    // SCL pins for 4 IMUs

// Sampling parameters
const int SAMPLE_RATE = 1000;  // 1000 Hz sampling
const int WINDOW_SIZE = 50;    // 50ms window (50 samples)
const int NUM_EMG = 8;         // 8 EMG channels
const int NUM_IMU = 4;         // 4 IMU sensors

// Data buffers
int emg_buffer[NUM_EMG][WINDOW_SIZE];
int16_t acc_buffer[NUM_IMU][3][WINDOW_SIZE];  // 4 IMUs, 3 axes each
int16_t gyro_buffer[NUM_IMU][3][WINDOW_SIZE]; // 4 IMUs, 3 axes each

int sample_count = 0;
unsigned long last_sample_time = 0;
bool wifi_connected = false;

// Muscle group mapping
const char* muscle_names[8] = {
  "L_Thigh_1", "L_Thigh_2", "R_Thigh_1", "R_Thigh_2",
  "L_Calf_1", "L_Calf_2", "R_Calf_1", "R_Calf_2"
};

const char* imu_locations[4] = {
  "L_Thigh", "R_Thigh", "L_Calf", "R_Calf"
};

void setup() {
  Serial.begin(115200);
  
  // Initialize EMG pins
  for (int i = 0; i < NUM_EMG; i++) {
    pinMode(EMG_PINS[i], INPUT);
  }
  
  // Initialize IMU sensors
  initializeIMUs();
  
  // Initialize WiFi
  initializeWiFi();
  
  // Initialize WebSocket
  webSocket.begin();
  webSocket.onEvent(webSocketEvent);
  
  Serial.println("ESP32_GAIT_SENSOR_READY");
  delay(1000);
}

void loop() {
  webSocket.loop();
  
  unsigned long current_time = micros();
  
  // Sample at 1000 Hz (1000 microseconds interval)
  if (current_time - last_sample_time >= 1000) {
    
    // Read EMG data from all 8 sensors
    for (int i = 0; i < NUM_EMG; i++) {
      emg_buffer[i][sample_count] = analogRead(EMG_PINS[i]);
    }
    
    // Read IMU data from all 4 sensors
    for (int imu = 0; imu < NUM_IMU; imu++) {
      int16_t ax, ay, az, gx, gy, gz;
      
      // Select appropriate I2C bus and read IMU
      selectI2CBus(imu);
      imu_sensors[imu].getMotion6(&ax, &ay, &az, &gx, &gy, &gz);
      
      acc_buffer[imu][0][sample_count] = ax;
      acc_buffer[imu][1][sample_count] = ay;
      acc_buffer[imu][2][sample_count] = az;
      gyro_buffer[imu][0][sample_count] = gx;
      gyro_buffer[imu][1][sample_count] = gy;
      gyro_buffer[imu][2][sample_count] = gz;
    }
    
    sample_count++;
    last_sample_time = current_time;
    
    // When window is full, send data and reset
    if (sample_count >= WINDOW_SIZE) {
      sendDataWindow();
      sample_count = 0;
    }
  }
}

void initializeIMUs() {
  Serial.println("Initializing 4 IMU sensors...");
  
  for (int i = 0; i < NUM_IMU; i++) {
    // Initialize I2C for each IMU
    Wire.begin(I2C_SDA_PINS[i], I2C_SCL_PINS[i]);
    imu_sensors[i].initialize();
    
    if (imu_sensors[i].testConnection()) {
      Serial.printf("IMU %d (%s) connected successfully\n", i, imu_locations[i]);
    } else {
      Serial.printf("ERROR: IMU %d (%s) connection failed\n", i, imu_locations[i]);
    }
    delay(100);
  }
}

void selectI2CBus(int imu_index) {
  // Switch I2C bus for the specific IMU
  Wire.begin(I2C_SDA_PINS[imu_index], I2C_SCL_PINS[imu_index]);
}

void initializeWiFi() {
  Serial.printf("Connecting to WiFi: %s\n", ssid);
  WiFi.begin(ssid, password);
  
  int attempts = 0;
  while (WiFi.status() != WL_CONNECTED && attempts < 20) {
    delay(500);
    Serial.print(".");
    attempts++;
  }
  
  if (WiFi.status() == WL_CONNECTED) {
    wifi_connected = true;
    Serial.printf("\nWiFi connected! IP: %s\n", WiFi.localIP().toString().c_str());
    Serial.printf("WebSocket server: ws://%s:81\n", WiFi.localIP().toString().c_str());
  } else {
    wifi_connected = false;
    Serial.println("\nWiFi connection failed - using Serial only");
  }
}

void sendDataWindow() {
  // Create JSON data packet
  DynamicJsonDocument doc(8192);
  
  doc["timestamp"] = millis();
  doc["window_size"] = WINDOW_SIZE;
  doc["sample_rate"] = SAMPLE_RATE;
  
  // Add EMG data
  JsonArray emg_data = doc.createNestedArray("emg");
  for (int muscle = 0; muscle < NUM_EMG; muscle++) {
    JsonObject muscle_obj = emg_data.createNestedObject();
    muscle_obj["muscle"] = muscle_names[muscle];
    muscle_obj["channel"] = muscle;
    
    JsonArray samples = muscle_obj.createNestedArray("samples");
    for (int i = 0; i < WINDOW_SIZE; i++) {
      samples.add(emg_buffer[muscle][i]);
    }
  }
  
  // Add IMU data
  JsonArray imu_data = doc.createNestedArray("imu");
  for (int imu = 0; imu < NUM_IMU; imu++) {
    JsonObject imu_obj = imu_data.createNestedObject();
    imu_obj["location"] = imu_locations[imu];
    imu_obj["sensor"] = imu;
    
    // Accelerometer data
    JsonObject acc_obj = imu_obj.createNestedObject("accelerometer");
    JsonArray acc_x = acc_obj.createNestedArray("x");
    JsonArray acc_y = acc_obj.createNestedArray("y");
    JsonArray acc_z = acc_obj.createNestedArray("z");
    
    for (int i = 0; i < WINDOW_SIZE; i++) {
      acc_x.add(acc_buffer[imu][0][i]);
      acc_y.add(acc_buffer[imu][1][i]);
      acc_z.add(acc_buffer[imu][2][i]);
    }
    
    // Gyroscope data
    JsonObject gyro_obj = imu_obj.createNestedObject("gyroscope");
    JsonArray gyro_x = gyro_obj.createNestedArray("x");
    JsonArray gyro_y = gyro_obj.createNestedArray("y");
    JsonArray gyro_z = gyro_obj.createNestedArray("z");
    
    for (int i = 0; i < WINDOW_SIZE; i++) {
      gyro_x.add(gyro_buffer[imu][0][i]);
      gyro_y.add(gyro_buffer[imu][1][i]);
      gyro_z.add(gyro_buffer[imu][2][i]);
    }
  }
  
  // Send via WebSocket (WiFi)
  if (wifi_connected) {
    String json_string;
    serializeJson(doc, json_string);
    webSocket.broadcastTXT(json_string);
  }
  
  // Also send via Serial for debugging
  Serial.println("DATA_START");
  serializeJson(doc, Serial);
  Serial.println("\nDATA_END");
}

void webSocketEvent(uint8_t num, WStype_t type, uint8_t * payload, size_t length) {
  switch(type) {
    case WStype_DISCONNECTED:
      Serial.printf("Client %u disconnected\n", num);
      break;
      
    case WStype_CONNECTED:
      {
        IPAddress ip = webSocket.remoteIP(num);
        Serial.printf("Client %u connected from %s\n", num, ip.toString().c_str());
        
        // Send welcome message
        DynamicJsonDocument welcome(512);
        welcome["message"] = "ESP32 Gait Sensor Connected";
        welcome["emg_channels"] = NUM_EMG;
        welcome["imu_sensors"] = NUM_IMU;
        welcome["sample_rate"] = SAMPLE_RATE;
        welcome["window_size"] = WINDOW_SIZE;
        
        String welcome_string;
        serializeJson(welcome, welcome_string);
        webSocket.sendTXT(num, welcome_string);
      }
      break;
      
    case WStype_TEXT:
      Serial.printf("Received: %s\n", payload);
      break;
      
    default:
      break;
  }
}
