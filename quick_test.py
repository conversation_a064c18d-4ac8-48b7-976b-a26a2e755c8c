import os
import pandas as pd
import numpy as np
import pickle
from sklearn.metrics import accuracy_score
import warnings
warnings.filterwarnings('ignore')

print("🚀 QUICK MODEL TEST")
print("="*30)

def quick_test():
    """Quick test of the trained model."""
    
    # Check if model exists
    model_file = "ultra_gait_classifier_ensemble.pkl"
    if not os.path.exists(model_file):
        print("❌ Model file not found! Please run ml_ultra.py first.")
        return
    
    # Load model
    try:
        with open(model_file, 'rb') as f:
            model_data = pickle.load(f)
        print(f"✅ Model loaded successfully")
        print(f"📊 Trained on {model_data['training_samples']:,} samples")
        print(f"🔍 Uses {len(model_data['selected_features'])} features")
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return
    
    # Test with a small sample from AB2948
    test_subject = "AB2948"
    test_path = f"datasets/{test_subject}/{test_subject}/Features"
    
    if not os.path.exists(test_path):
        print(f"❌ Test data not found: {test_path}")
        return
    
    # Load one test file
    test_files = [f for f in os.listdir(test_path) if f.endswith('.csv')]
    if not test_files:
        print("❌ No CSV files found in test directory")
        return
    
    test_file = test_files[0]
    print(f"📂 Testing with: {test_file}")
    
    try:
        # Load test data
        df = pd.read_csv(f"{test_path}/{test_file}")
        df['Subject'] = test_subject
        df['Trial'] = int(test_file.split('_')[2])
        
        print(f"📊 Test data shape: {df.shape}")
        
        # Create feature matrix using same features as training
        training_features = model_data['selected_features']
        X_test = pd.DataFrame(index=df.index)
        
        available_features = 0
        for feature in training_features:
            if feature in df.columns:
                X_test[feature] = df[feature]
                available_features += 1
            else:
                X_test[feature] = 0
        
        print(f"🔍 Features available: {available_features}/{len(training_features)}")
        
        # Clean and scale data
        X_test_clean = X_test.fillna(0)
        scaler = model_data['scaler']
        X_test_scaled = scaler.transform(X_test_clean)
        
        # Handle any remaining NaN values
        if np.isnan(X_test_scaled).any():
            X_test_scaled = np.nan_to_num(X_test_scaled, nan=0.0)
        
        print(f"📊 Processed test data: {X_test_scaled.shape}")
        
        # Test Random Forest
        rf_model = model_data['rf_model']
        rf_pred = rf_model.predict(X_test_scaled)
        
        # Test SVM
        svm_model = model_data['svm_model']
        svm_pred = svm_model.predict(X_test_scaled)
        
        # Get label encoder
        le = model_data['label_encoder']
        
        # Convert predictions to class names
        rf_classes = le.inverse_transform(rf_pred)
        svm_classes = le.inverse_transform(svm_pred)
        
        # Show predictions
        print(f"\n🌲 Random Forest Predictions:")
        rf_unique, rf_counts = np.unique(rf_classes, return_counts=True)
        for cls, count in zip(rf_unique, rf_counts):
            print(f"   {cls}: {count} samples ({count/len(rf_classes)*100:.1f}%)")
        
        print(f"\n🤖 SVM Predictions:")
        svm_unique, svm_counts = np.unique(svm_classes, return_counts=True)
        for cls, count in zip(svm_unique, svm_counts):
            print(f"   {cls}: {count} samples ({count/len(svm_classes)*100:.1f}%)")
        
        # Agreement between models
        agreement = np.sum(rf_pred == svm_pred) / len(rf_pred)
        print(f"\n🤝 Model Agreement: {agreement:.4f} ({agreement*100:.2f}%)")
        
        # Most common predictions
        rf_most_common = rf_unique[np.argmax(rf_counts)]
        svm_most_common = svm_unique[np.argmax(svm_counts)]
        
        print(f"\n🎯 Dominant Predictions:")
        print(f"   Random Forest: {rf_most_common}")
        print(f"   SVM: {svm_most_common}")
        
        if rf_most_common == svm_most_common:
            print(f"✅ Both models agree: {rf_most_common}")
        else:
            print(f"⚠️ Models disagree")
        
        print(f"\n✅ Quick test completed successfully!")
        print(f"📊 Tested {len(df)} samples from {test_subject}")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    quick_test()
