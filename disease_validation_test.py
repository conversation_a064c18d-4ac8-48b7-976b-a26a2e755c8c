import os
import pandas as pd
import numpy as np
import pickle
from sklearn.metrics import accuracy_score
import warnings
warnings.filterwarnings('ignore')

print("🔬 COMPREHENSIVE DISEASE VALIDATION TEST")
print("🎯 Testing All Clinical Diseases for Identification Accuracy")
print("="*70)

# ALL DISEASES TO TEST (as requested by user)
ALL_DISEASES = {
    # Neurological Disorders
    'Normal_Gait': {'emg_factor': 1.0, 'asymmetry': 0.1, 'stability': 0.5, 'coordination': 0.6},
    'Stroke_Hemiparetic': {'emg_factor': 0.6, 'asymmetry': 0.7, 'stability': 0.3, 'coordination': 0.4},
    'Cerebral_Palsy_Spastic': {'emg_factor': 1.8, 'asymmetry': 0.3, 'stability': 0.8, 'coordination': 0.3},
    'Parkinsonian_Gait': {'emg_factor': 0.3, 'asymmetry': 0.15, 'stability': 0.2, 'coordination': 0.2},
    'Multiple_Sclerosis': {'emg_factor': 0.8, 'asymmetry': 0.4, 'stability': 0.9, 'coordination': 0.3},
    'Peripheral_Neuropathy': {'emg_factor': 0.5, 'asymmetry': 0.2, 'stability': 0.4, 'coordination': 0.3},
    'Spinal_Cord_Injury': {'emg_factor': 0.4, 'asymmetry': 0.5, 'stability': 0.6, 'coordination': 0.2},
    
    # Musculoskeletal Conditions
    'Arthritis_Antalgic': {'emg_factor': 0.7, 'asymmetry': 0.5, 'stability': 0.4, 'coordination': 0.5},
    'ACL_Injury': {'emg_factor': 1.2, 'asymmetry': 0.4, 'stability': 0.6, 'coordination': 0.4},
    'Lower_Limb_Fracture': {'emg_factor': 0.6, 'asymmetry': 0.6, 'stability': 0.3, 'coordination': 0.4},
    'Limb_Length_Discrepancy': {'emg_factor': 0.9, 'asymmetry': 0.8, 'stability': 0.5, 'coordination': 0.5},
    'Foot_Deformities': {'emg_factor': 1.1, 'asymmetry': 0.3, 'stability': 0.7, 'coordination': 0.4},
    'Scoliosis_Gait': {'emg_factor': 0.8, 'asymmetry': 0.4, 'stability': 0.6, 'coordination': 0.4},
    
    # Balance and Vestibular Disorders
    'Vestibular_Dysfunction': {'emg_factor': 0.7, 'asymmetry': 0.3, 'stability': 0.9, 'coordination': 0.2},
    'Balance_Impairment': {'emg_factor': 0.6, 'asymmetry': 0.2, 'stability': 0.8, 'coordination': 0.3},
    
    # Geriatric Conditions
    'Frailty_Gait': {'emg_factor': 0.4, 'asymmetry': 0.2, 'stability': 0.3, 'coordination': 0.3},
    'Fear_of_Falling': {'emg_factor': 0.5, 'asymmetry': 0.15, 'stability': 0.4, 'coordination': 0.4},
    
    # Developmental Disorders
    'Developmental_Delays': {'emg_factor': 0.6, 'asymmetry': 0.25, 'stability': 0.5, 'coordination': 0.3},
    'Toe_Walking': {'emg_factor': 1.3, 'asymmetry': 0.2, 'stability': 0.6, 'coordination': 0.4}
}

def load_clinical_model():
    """Load the trained clinical model."""
    model_file = "final_clinical_gait_classifier.pkl"
    
    if not os.path.exists(model_file):
        print(f"❌ Clinical model not found: {model_file}")
        print("Please run final_gait_classifier.py first!")
        return None
    
    try:
        with open(model_file, 'rb') as f:
            model_data = pickle.load(f)
        
        print(f"✅ Clinical model loaded: {model_file}")
        print(f"📊 Trained on {model_data['training_samples']:,} samples")
        print(f"🔍 Uses {len(model_data['selected_features'])} features")
        print(f"🏥 Clinical diseases: {len(model_data['clinical_diseases'])}")
        
        return model_data
    
    except Exception as e:
        print(f"❌ Error loading clinical model: {e}")
        return None

def create_synthetic_disease_data(disease_name, disease_params, n_samples=200):
    """Create synthetic data that matches disease characteristics."""
    
    # Base feature matrix (matching expected clinical features)
    n_features = 112  # Match clinical feature count
    
    # Create base features with normal distribution
    features = np.random.normal(0.5, 0.2, (n_samples, n_features))
    
    # Apply disease-specific modifications
    emg_factor = disease_params['emg_factor']
    asymmetry = disease_params['asymmetry']
    stability = disease_params['stability']
    coordination = disease_params['coordination']
    
    # EMG features (first 40 features)
    features[:, :40] *= emg_factor
    
    # Add asymmetry patterns (left vs right)
    left_indices = list(range(0, 20))  # Left side EMG
    right_indices = list(range(20, 40))  # Right side EMG
    
    if asymmetry > 0.3:  # High asymmetry diseases
        features[:, left_indices] *= (1 - asymmetry)
        features[:, right_indices] *= (1 + asymmetry * 0.5)
    
    # IMU features (features 40-112)
    imu_features = features[:, 40:]
    
    # Apply stability modifications
    if stability > 0.7:  # High instability
        noise = np.random.normal(0, stability, imu_features.shape)
        features[:, 40:] = imu_features + noise
    elif stability < 0.3:  # Very stable/rigid
        features[:, 40:] = imu_features * stability
    
    # Apply coordination modifications
    if coordination < 0.3:  # Poor coordination
        coord_noise = np.random.normal(0, 1-coordination, imu_features.shape)
        features[:, 40:] += coord_noise * 0.3
    
    # Ensure positive values and proper scaling
    features = np.abs(features)
    features = np.clip(features, 0, 3)
    
    return features

def test_disease_identification(model_data):
    """Test the model's ability to identify each disease."""
    print("\n🔬 TESTING DISEASE IDENTIFICATION CAPABILITIES")
    print("="*60)
    
    # Load model components
    rf_model = model_data['rf_model']
    svm_model = model_data['svm_model']
    scaler = model_data['scaler']
    le = model_data['label_encoder']
    
    # Get available classes in the model
    available_classes = set(le.classes_)
    print(f"📊 Model trained on {len(available_classes)} classes:")
    for cls in sorted(available_classes):
        print(f"   ✅ {cls}")
    
    print(f"\n🧪 Testing {len(ALL_DISEASES)} disease patterns...")
    
    results = {}
    
    for disease_name, disease_params in ALL_DISEASES.items():
        print(f"\n🔍 Testing {disease_name}...")
        
        # Create synthetic test data for this disease
        test_features = create_synthetic_disease_data(disease_name, disease_params)
        
        # Scale features
        test_scaled = scaler.transform(test_features)
        if np.isnan(test_scaled).any():
            test_scaled = np.nan_to_num(test_scaled, nan=0.0)
        
        # Predict with both models
        rf_pred = rf_model.predict(test_scaled)
        svm_pred = svm_model.predict(test_scaled)
        
        # Convert predictions to class names
        rf_classes = le.inverse_transform(rf_pred)
        svm_classes = le.inverse_transform(svm_pred)
        
        # Calculate identification accuracy
        if disease_name in available_classes:
            target_class_idx = le.transform([disease_name])[0]
            rf_accuracy = np.mean(rf_pred == target_class_idx)
            svm_accuracy = np.mean(svm_pred == target_class_idx)
            status = "✅ TRAINED"
        else:
            rf_accuracy = 0.0
            svm_accuracy = 0.0
            status = "❌ NOT TRAINED"
        
        # Get prediction distribution
        rf_unique, rf_counts = np.unique(rf_classes, return_counts=True)
        svm_unique, svm_counts = np.unique(svm_classes, return_counts=True)
        
        rf_dist = dict(zip(rf_unique, rf_counts))
        svm_dist = dict(zip(svm_unique, svm_counts))
        
        # Store results
        results[disease_name] = {
            'status': status,
            'rf_accuracy': rf_accuracy,
            'svm_accuracy': svm_accuracy,
            'rf_predictions': rf_dist,
            'svm_predictions': svm_dist,
            'best_accuracy': max(rf_accuracy, svm_accuracy)
        }
        
        # Display results
        print(f"   Status: {status}")
        print(f"   RF Accuracy: {rf_accuracy:.4f} ({rf_accuracy*100:.2f}%)")
        print(f"   SVM Accuracy: {svm_accuracy:.4f} ({svm_accuracy*100:.2f}%)")
        print(f"   RF Top Prediction: {max(rf_dist, key=rf_dist.get)} ({rf_dist[max(rf_dist, key=rf_dist.get)]} samples)")
        print(f"   SVM Top Prediction: {max(svm_dist, key=svm_dist.get)} ({svm_dist[max(svm_dist, key=svm_dist.get)]} samples)")
    
    return results

def generate_disease_report(results):
    """Generate comprehensive disease identification report."""
    print(f"\n📋 COMPREHENSIVE DISEASE IDENTIFICATION REPORT")
    print("="*70)
    
    # Categorize results
    trained_diseases = []
    untrained_diseases = []
    high_accuracy = []
    medium_accuracy = []
    low_accuracy = []
    
    for disease, result in results.items():
        if "TRAINED" in result['status']:
            trained_diseases.append(disease)
            accuracy = result['best_accuracy']
            if accuracy >= 0.8:
                high_accuracy.append((disease, accuracy))
            elif accuracy >= 0.5:
                medium_accuracy.append((disease, accuracy))
            else:
                low_accuracy.append((disease, accuracy))
        else:
            untrained_diseases.append(disease)
    
    print(f"\n🎯 SUMMARY STATISTICS:")
    print(f"   Total diseases tested: {len(ALL_DISEASES)}")
    print(f"   Diseases in training: {len(trained_diseases)}")
    print(f"   Diseases not trained: {len(untrained_diseases)}")
    print(f"   High accuracy (≥80%): {len(high_accuracy)}")
    print(f"   Medium accuracy (50-79%): {len(medium_accuracy)}")
    print(f"   Low accuracy (<50%): {len(low_accuracy)}")
    
    print(f"\n✅ HIGH ACCURACY DISEASES (≥80%):")
    for disease, accuracy in sorted(high_accuracy, key=lambda x: x[1], reverse=True):
        print(f"   🎯 {disease}: {accuracy*100:.1f}%")
    
    print(f"\n⚠️ MEDIUM ACCURACY DISEASES (50-79%):")
    for disease, accuracy in sorted(medium_accuracy, key=lambda x: x[1], reverse=True):
        print(f"   📈 {disease}: {accuracy*100:.1f}%")
    
    print(f"\n❌ LOW ACCURACY DISEASES (<50%):")
    for disease, accuracy in sorted(low_accuracy, key=lambda x: x[1], reverse=True):
        print(f"   📉 {disease}: {accuracy*100:.1f}%")
    
    print(f"\n🚫 DISEASES NOT IN TRAINING:")
    for disease in sorted(untrained_diseases):
        print(f"   ❌ {disease}")
    
    # Overall assessment
    if len(high_accuracy) >= len(trained_diseases) * 0.8:
        print(f"\n🎉 EXCELLENT: {len(high_accuracy)}/{len(trained_diseases)} diseases have high accuracy!")
    elif len(high_accuracy) >= len(trained_diseases) * 0.6:
        print(f"\n👍 GOOD: {len(high_accuracy)}/{len(trained_diseases)} diseases have high accuracy!")
    else:
        print(f"\n📈 NEEDS IMPROVEMENT: Only {len(high_accuracy)}/{len(trained_diseases)} diseases have high accuracy")

def main():
    print("🚀 Starting comprehensive disease validation...")
    
    # Load clinical model
    model_data = load_clinical_model()
    if model_data is None:
        return
    
    # Test disease identification
    results = test_disease_identification(model_data)
    
    # Generate comprehensive report
    generate_disease_report(results)
    
    print(f"\n🏥 CLINICAL VALIDATION COMPLETE!")
    print(f"📊 Model file: final_clinical_gait_classifier.pkl")
    print(f"🔬 Tested {len(ALL_DISEASES)} disease patterns")
    print(f"✅ Clinical gait classifier validation finished!")

if __name__ == "__main__":
    main()
