/*
🔬 REAL-TIME GAIT SENSOR DATA COLLECTION
Arduino code for AD8232 EMG + IMU sensors
Sends data to Python ML model via Serial
*/

#include <Wire.h>
#include <MPU6050.h>

// AD8232 EMG sensor pins (adjust based on your setup)
const int EMG_R_SOLEUS = A0;
const int EMG_L_SOLEUS = A1;
const int EMG_R_RECTUS = A2;
const int EMG_L_RECTUS = A3;
const int EMG_R_GASTRO = A4;
const int EMG_L_GASTRO = A5;

// MPU6050 IMU sensor
MPU6050 mpu;

// Sampling parameters
const int SAMPLE_RATE = 1000;  // 1000 Hz sampling
const int WINDOW_SIZE = 50;    // 50ms window (50 samples)
const int NUM_MUSCLES = 6;     // Number of EMG channels

// Data buffers
int emg_buffer[NUM_MUSCLES][WINDOW_SIZE];
int16_t acc_buffer[3][WINDOW_SIZE];  // X, Y, Z
int16_t gyro_buffer[3][WINDOW_SIZE]; // X, Y, Z

int sample_count = 0;
unsigned long last_sample_time = 0;

void setup() {
  Serial.begin(115200);
  
  // Initialize IMU
  Wire.begin();
  mpu.initialize();
  
  if (!mpu.testConnection()) {
    Serial.println("ERROR: MPU6050 connection failed");
    while(1);
  }
  
  // Initialize EMG pins
  pinMode(EMG_R_SOLEUS, INPUT);
  pinMode(EMG_L_SOLEUS, INPUT);
  pinMode(EMG_R_RECTUS, INPUT);
  pinMode(EMG_L_RECTUS, INPUT);
  pinMode(EMG_R_GASTRO, INPUT);
  pinMode(EMG_L_GASTRO, INPUT);
  
  Serial.println("GAIT_SENSOR_READY");
  delay(1000);
}

void loop() {
  unsigned long current_time = micros();
  
  // Sample at 1000 Hz (1000 microseconds interval)
  if (current_time - last_sample_time >= 1000) {
    
    // Read EMG data
    emg_buffer[0][sample_count] = analogRead(EMG_R_SOLEUS);
    emg_buffer[1][sample_count] = analogRead(EMG_L_SOLEUS);
    emg_buffer[2][sample_count] = analogRead(EMG_R_RECTUS);
    emg_buffer[3][sample_count] = analogRead(EMG_L_RECTUS);
    emg_buffer[4][sample_count] = analogRead(EMG_R_GASTRO);
    emg_buffer[5][sample_count] = analogRead(EMG_L_GASTRO);
    
    // Read IMU data
    int16_t ax, ay, az, gx, gy, gz;
    mpu.getMotion6(&ax, &ay, &az, &gx, &gy, &gz);
    
    acc_buffer[0][sample_count] = ax;
    acc_buffer[1][sample_count] = ay;
    acc_buffer[2][sample_count] = az;
    gyro_buffer[0][sample_count] = gx;
    gyro_buffer[1][sample_count] = gy;
    gyro_buffer[2][sample_count] = gz;
    
    sample_count++;
    last_sample_time = current_time;
    
    // When window is full, send data and reset
    if (sample_count >= WINDOW_SIZE) {
      sendDataWindow();
      sample_count = 0;
    }
  }
}

void sendDataWindow() {
  // Send data in JSON format for easy parsing
  Serial.println("DATA_START");
  
  // Send EMG data
  for (int muscle = 0; muscle < NUM_MUSCLES; muscle++) {
    Serial.print("EMG_");
    Serial.print(muscle);
    Serial.print(":");
    for (int i = 0; i < WINDOW_SIZE; i++) {
      Serial.print(emg_buffer[muscle][i]);
      if (i < WINDOW_SIZE - 1) Serial.print(",");
    }
    Serial.println();
  }
  
  // Send ACC data
  for (int axis = 0; axis < 3; axis++) {
    Serial.print("ACC_");
    Serial.print(axis);
    Serial.print(":");
    for (int i = 0; i < WINDOW_SIZE; i++) {
      Serial.print(acc_buffer[axis][i]);
      if (i < WINDOW_SIZE - 1) Serial.print(",");
    }
    Serial.println();
  }
  
  // Send GYRO data
  for (int axis = 0; axis < 3; axis++) {
    Serial.print("GYRO_");
    Serial.print(axis);
    Serial.print(":");
    for (int i = 0; i < WINDOW_SIZE; i++) {
      Serial.print(gyro_buffer[axis][i]);
      if (i < WINDOW_SIZE - 1) Serial.print(",");
    }
    Serial.println();
  }
  
  Serial.println("DATA_END");
}
