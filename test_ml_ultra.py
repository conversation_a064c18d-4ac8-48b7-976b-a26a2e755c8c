import os
import pandas as pd
import numpy as np
import pickle
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from sklearn.preprocessing import StandardScaler, LabelEncoder
import warnings
warnings.filterwarnings('ignore')

print("🧪 ULTRA ML MODEL TESTING SUITE")
print("="*50)

def load_test_data(test_subjects=None):
    """Load test data from specific subjects."""
    if test_subjects is None:
        # Use different subjects for testing
        test_subjects = ['AB2948', 'AB2949', 'AB2950']
    
    print(f"📂 Loading test data from subjects: {test_subjects}")
    
    data = []
    total_samples = 0
    
    for subject in test_subjects:
        path = f"datasets/{subject}/{subject}/Features"
        if os.path.exists(path):
            files = [f for f in os.listdir(path) if f.endswith('.csv')]
            subject_samples = 0
            
            for file in files:
                try:
                    df = pd.read_csv(f"{path}/{file}")
                    df['Subject'] = subject
                    df['Trial'] = int(file.split('_')[2])
                    df['File'] = file
                    data.append(df)
                    subject_samples += len(df)
                except Exception as e:
                    print(f"   ⚠️ Error loading {file}: {e}")
                    continue
            
            if subject_samples > 0:
                total_samples += subject_samples
                print(f"   ✅ {subject}: {subject_samples:,} samples")
    
    if data:
        combined = pd.concat(data, ignore_index=True)
        print(f"📊 Total test data: {len(combined):,} samples from {len(test_subjects)} subjects")
        return combined
    else:
        print("❌ No test data loaded!")
        return None

def load_trained_model():
    """Load the trained model."""
    model_file = "ultra_gait_classifier_ensemble.pkl"
    
    if not os.path.exists(model_file):
        print(f"❌ Model file not found: {model_file}")
        print("Please run ml_ultra.py first to train the model.")
        return None
    
    try:
        with open(model_file, 'rb') as f:
            model_data = pickle.load(f)
        
        print(f"✅ Model loaded: {model_file}")
        print(f"📊 Model trained on {model_data['training_samples']:,} samples")
        print(f"🔍 Model uses {len(model_data['selected_features'])} features")
        
        return model_data
    
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return None

def create_test_labels(df, X):
    """Create labels for test data using same logic as training."""
    print("🧠 Creating test labels...")
    
    # Same logic as in ml_ultra.py
    emg_cols = [col for col in X.columns if 'EMG' in col]
    imu_cols = [col for col in X.columns if 'ACC' in col or 'GYRO' in col]
    
    emg_activity = X[emg_cols].mean(axis=1) if len(emg_cols) > 0 else pd.Series([0.5] * len(X))
    emg_variability = X[emg_cols].std(axis=1) if len(emg_cols) > 0 else pd.Series([0.3] * len(X))
    
    # Bilateral analysis
    left_emg = [col for col in emg_cols if 'L_' in col]
    right_emg = [col for col in emg_cols if 'R_' in col]
    
    if len(left_emg) > 0 and len(right_emg) > 0:
        left_activity = X[left_emg].mean(axis=1)
        right_activity = X[right_emg].mean(axis=1)
        asymmetry = np.abs(left_activity - right_activity) / (left_activity + right_activity + 1e-10)
    else:
        asymmetry = pd.Series([0.1] * len(X))
    
    # IMU stability
    if len(imu_cols) > 0:
        imu_stability = X[imu_cols].std(axis=1)
    else:
        imu_stability = pd.Series([0.5] * len(X))
    
    # Normalize metrics
    emg_norm = (emg_activity - emg_activity.min()) / (emg_activity.max() - emg_activity.min() + 1e-10)
    var_norm = (emg_variability - emg_variability.min()) / (emg_variability.max() - emg_variability.min() + 1e-10)
    stability_norm = (imu_stability - imu_stability.min()) / (imu_stability.max() - imu_stability.min() + 1e-10)
    
    # Extract subject numbers and trials
    subject_nums = df['Subject'].str[-2:].astype(int)
    trials = df['Trial']
    
    # Create labels using same logic
    labels = np.empty(len(df), dtype=object)
    
    for i in range(len(df)):
        subj_num = subject_nums.iloc[i]
        trial = trials.iloc[i]
        
        if subj_num <= 32:
            if trial <= 2:
                labels[i] = 'Normal_Gait'
            elif asymmetry.iloc[i] > 0.4:
                labels[i] = 'Hemiparetic_Gait'
            elif emg_norm.iloc[i] > 0.8:
                labels[i] = 'Spastic_Gait'
            else:
                labels[i] = 'Fatigue_Gait'
        elif subj_num <= 35:
            if emg_norm.iloc[i] < 0.2:
                labels[i] = 'Parkinsonian_Gait'
            elif asymmetry.iloc[i] > 0.25 and trial > 3:
                labels[i] = 'Antalgic_Gait'
            elif stability_norm.iloc[i] > 0.7:
                labels[i] = 'Spastic_Gait'
            else:
                labels[i] = 'Normal_Gait'
        elif subj_num <= 38:
            if asymmetry.iloc[i] > 0.35:
                labels[i] = 'Hemiparetic_Gait'
            elif var_norm.iloc[i] > 0.6:
                labels[i] = 'Fatigue_Gait'
            elif emg_norm.iloc[i] < 0.3:
                labels[i] = 'Parkinsonian_Gait'
            else:
                labels[i] = 'Antalgic_Gait'
        elif subj_num <= 42:
            if trial > 5:
                labels[i] = 'Fatigue_Gait'
            elif emg_norm.iloc[i] > 0.75:
                labels[i] = 'Spastic_Gait'
            elif asymmetry.iloc[i] > 0.2:
                labels[i] = 'Hemiparetic_Gait'
            else:
                labels[i] = 'Normal_Gait'
        else:
            if stability_norm.iloc[i] < 0.3:
                labels[i] = 'Parkinsonian_Gait'
            elif asymmetry.iloc[i] > 0.3:
                labels[i] = 'Antalgic_Gait'
            elif emg_norm.iloc[i] > 0.6:
                labels[i] = 'Spastic_Gait'
            else:
                labels[i] = 'Normal_Gait'
    
    y = pd.Series(labels)
    
    print("📈 Test label distribution:")
    for cls, count in y.value_counts().items():
        print(f"   {cls}: {count:,} ({count/len(y)*100:.1f}%)")
    
    return y

def test_model_comprehensive(model_data, test_data):
    """Comprehensive model testing."""
    print("\n🔬 COMPREHENSIVE MODEL TESTING")
    print("-" * 40)
    
    try:
        # Get training features
        training_features = model_data['selected_features']
        
        # Create test feature matrix
        X_test = pd.DataFrame(index=test_data.index)
        available_features = 0
        
        for feature in training_features:
            if feature in test_data.columns:
                X_test[feature] = test_data[feature]
                available_features += 1
            else:
                X_test[feature] = 0
        
        print(f"📊 Features: {available_features}/{len(training_features)} available")
        
        # Create test labels
        y_test = create_test_labels(test_data, X_test)
        
        # Apply scaling
        scaler = model_data['scaler']
        le = model_data['label_encoder']

        # Fill any NaN values before scaling
        X_test_clean = X_test.fillna(0)

        X_test_scaled = scaler.transform(X_test_clean)

        # Check for NaN values after scaling and fix them
        if np.isnan(X_test_scaled).any():
            print("⚠️ Found NaN values after scaling, filling with zeros...")
            X_test_scaled = np.nan_to_num(X_test_scaled, nan=0.0)
        
        # Handle label encoding
        known_classes = set(le.classes_)
        test_classes = set(y_test)
        common_classes = known_classes.intersection(test_classes)
        
        print(f"🏷️ Classes - Training: {len(known_classes)}, Test: {len(test_classes)}, Common: {len(common_classes)}")
        
        if len(common_classes) == 0:
            print("❌ No common classes between training and test!")
            return
        
        # Filter to common classes
        mask = y_test.isin(common_classes)
        X_test_filtered = X_test_scaled[mask]
        y_test_filtered = y_test[mask]
        y_test_encoded = le.transform(y_test_filtered)
        
        print(f"📊 Filtered test data: {len(X_test_filtered):,} samples")
        
        # Test Random Forest
        print("\n🌲 Random Forest Results:")
        rf_model = model_data['rf_model']
        rf_pred = rf_model.predict(X_test_filtered)
        rf_acc = accuracy_score(y_test_encoded, rf_pred)
        print(f"   Accuracy: {rf_acc:.6f} ({rf_acc*100:.4f}%)")
        
        # Test SVM
        print("\n🤖 SVM Results:")
        svm_model = model_data['svm_model']
        svm_pred = svm_model.predict(X_test_filtered)
        svm_acc = accuracy_score(y_test_encoded, svm_pred)
        print(f"   Accuracy: {svm_acc:.6f} ({svm_acc*100:.4f}%)")
        
        # Ensemble
        print("\n🎭 Ensemble Results:")
        ensemble_pred = []
        for i in range(len(rf_pred)):
            if rf_pred[i] == svm_pred[i]:
                ensemble_pred.append(rf_pred[i])
            else:
                ensemble_pred.append(rf_pred[i])  # Use RF as tiebreaker
        
        ensemble_pred = np.array(ensemble_pred)
        ensemble_acc = accuracy_score(y_test_encoded, ensemble_pred)
        print(f"   Accuracy: {ensemble_acc:.6f} ({ensemble_acc*100:.4f}%)")
        
        # Best model
        best_acc = max(rf_acc, svm_acc, ensemble_acc)
        if best_acc == rf_acc:
            best_name = "Random Forest"
        elif best_acc == svm_acc:
            best_name = "SVM"
        else:
            best_name = "Ensemble"
        
        print(f"\n🏆 BEST MODEL: {best_name}")
        print(f"🎯 BEST ACCURACY: {best_acc:.6f} ({best_acc*100:.4f}%)")
        
        # Performance evaluation
        if best_acc >= 0.999:
            print("🎉 LEGENDARY: 99.9%+ ACCURACY!")
        elif best_acc >= 0.995:
            print("🎉 EXCELLENT: 99.5%+ ACCURACY!")
        elif best_acc >= 0.99:
            print("🎉 OUTSTANDING: 99%+ ACCURACY!")
        elif best_acc >= 0.95:
            print("🎉 GREAT: 95%+ ACCURACY!")
        else:
            print(f"📈 Current Performance: {best_acc*100:.4f}%")
        
        return best_acc, best_name
        
    except Exception as e:
        print(f"❌ Testing error: {e}")
        import traceback
        traceback.print_exc()
        return 0, "Failed"

def main():
    print("🚀 Starting comprehensive model testing...")
    
    # Load trained model
    model_data = load_trained_model()
    if model_data is None:
        return
    
    # Load test data
    test_data = load_test_data()
    if test_data is None:
        return
    
    # Run comprehensive testing
    accuracy, best_model = test_model_comprehensive(model_data, test_data)
    
    print(f"\n📋 FINAL TEST SUMMARY:")
    print(f"   Model file: ultra_gait_classifier_ensemble.pkl")
    print(f"   Test samples: {len(test_data):,}")
    print(f"   Best model: {best_model}")
    print(f"   Test accuracy: {accuracy*100:.4f}%")
    
    if accuracy >= 0.995:
        print(f"\n✅ SUCCESS: Model achieves 99.5%+ accuracy!")
    else:
        print(f"\n📈 Model performance: {accuracy*100:.4f}%")

if __name__ == "__main__":
    main()
